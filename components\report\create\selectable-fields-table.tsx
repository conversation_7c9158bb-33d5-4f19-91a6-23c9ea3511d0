"use client";

import React, { useMemo, useState } from "react";
import {
  Input,
  Table,
  TableBody,
  TableCell,
  TableColumn,
  TableHeader,
  TableRow,
  Pagination,
  Chip,
  Spinner,
} from "@heroui/react";
import { Icon } from "@iconify/react";

import { FilterDropdown } from "@/components/projects/projects-table/filter-dropdown";
import { useAllFieldsQuery } from "@/graphql/schemas/generated";

interface SelectableFieldsTableProps {
  selectedFields: string[];
  onSelectionChange: (fields: string[]) => void;
}

export default function SelectableFieldsTable({
  selectedFields,
  onSelectionChange,
}: SelectableFieldsTableProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [page, setPage] = useState(1);
  const [activeFilters, setActiveFilters] = useState<Record<string, string[]>>(
    {},
  );
  const [sortConfig, setSortConfig] = useState<{
    column: string;
    direction: "asc" | "desc";
  } | null>(null);

  const { data: fieldsData, loading: fieldsLoading } = useAllFieldsQuery();

  const rowsPerPage = 10;

  const fields = fieldsData?.allFields || [];

  // Filter and search logic
  const filteredFields = useMemo(() => {
    let filtered = fields.filter((field) => {
      if (!field) return false;

      // Search filter
      const matchesSearch = field.name
        ?.toLowerCase()
        .includes(searchTerm.toLowerCase());

      // Active filters
      const matchesFilters = Object.entries(activeFilters).every(
        ([column, values]) => {
          if (values.length === 0) return true;

          switch (column) {
            case "name":
              return values.includes(field.name || "");
            case "type":
              return values.includes(field.type || "");
            case "phase":
              return values.includes(field.subphase?.phase?.name || "");
            case "subphase":
              return values.includes(field.subphase?.name || "");
            case "weight":
              return values.includes(field.weight?.toString() || "");
            default:
              return true;
          }
        },
      );

      return matchesSearch && matchesFilters;
    });

    return filtered;
  }, [fields, searchTerm, activeFilters]);

  // Sort logic
  const sortedFields = useMemo(() => {
    if (!sortConfig) return filteredFields;

    return [...filteredFields].sort((a, b) => {
      let aValue: any;
      let bValue: any;

      switch (sortConfig.column) {
        case "name":
          aValue = a?.name || "";
          bValue = b?.name || "";
          break;
        case "type":
          aValue = a?.type || "";
          bValue = b?.type || "";
          break;
        case "phase":
          aValue = a?.subphase?.phase?.name || "";
          bValue = b?.subphase?.phase?.name || "";
          break;
        case "subphase":
          aValue = a?.subphase?.name || "";
          bValue = b?.subphase?.name || "";
          break;
        case "weight":
          aValue = parseFloat(a?.weight?.toString() || "0");
          bValue = parseFloat(b?.weight?.toString() || "0");
          break;
        default:
          return 0;
      }

      if (aValue < bValue) {
        return sortConfig.direction === "asc" ? -1 : 1;
      }
      if (aValue > bValue) {
        return sortConfig.direction === "asc" ? 1 : -1;
      }

      return 0;
    });
  }, [filteredFields, sortConfig]);

  const pages = Math.ceil(sortedFields.length / rowsPerPage);
  const items = useMemo(() => {
    const start = (page - 1) * rowsPerPage;
    const end = start + rowsPerPage;

    return sortedFields.slice(start, end);
  }, [page, sortedFields]);

  // Helper functions
  const getUniqueValues = (column: string): string[] => {
    const values = fields
      .map((field) => {
        if (!field) return "";
        switch (column) {
          case "name":
            return field.name || "";
          case "type":
            return field.type || "";
          case "phase":
            return field.subphase?.phase?.name || "";
          case "subphase":
            return field.subphase?.name || "";
          case "weight":
            return field.weight?.toString() || "";
          default:
            return "";
        }
      })
      .filter((value) => value !== "");

    return Array.from(new Set(values)).sort();
  };

  const handleFilterChange = (column: string, values: string[]) => {
    setActiveFilters((prev) => ({
      ...prev,
      [column]: values,
    }));
    setPage(1);
  };

  const handleSort = (column: string, direction: "asc" | "desc") => {
    setSortConfig({ column, direction });
  };

  const handleSelectionChange = (keys: any) => {
    if (keys === "all") {
      const allKeys = sortedFields.map((field) => field?.id || "");
      onSelectionChange(allKeys);
    } else {
      const newSelection = Array.from(keys).map((key) => String(key));
      onSelectionChange(newSelection);
    }
  };

  if (fieldsLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spinner label="Cargando campos..." />
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Search Input */}
      <div className="flex gap-4 items-center">
        <Input
          className="max-w-sm"
          placeholder="Buscar campos..."
          startContent={<Icon icon="heroicons:magnifying-glass" width={16} />}
          value={searchTerm}
          onValueChange={setSearchTerm}
        />
        <div className="text-sm text-default-500">
          {selectedFields.length} campo(s) seleccionado(s)
        </div>
      </div>

      {/* Table */}
      <Table
        removeWrapper
        aria-label="Tabla de campos seleccionables"
        bottomContent={
          pages > 1 ? (
            <div className="flex w-full justify-center">
              <Pagination
                isCompact
                showControls
                showShadow
                color="primary"
                page={page}
                total={pages}
                onChange={setPage}
              />
            </div>
          ) : null
        }
        selectedKeys={new Set(selectedFields)}
        selectionMode="multiple"
        onSelectionChange={handleSelectionChange}
      >
        <TableHeader>
          <TableColumn>
            <FilterDropdown
              activeFilters={activeFilters}
              column="name"
              items={getUniqueValues("name")}
              sortConfig={sortConfig}
              title="Nombre"
              onFilter={handleFilterChange}
              onSort={handleSort}
            />
          </TableColumn>
          <TableColumn>Descripción</TableColumn>
          <TableColumn>
            <FilterDropdown
              activeFilters={activeFilters}
              column="type"
              displayText={{
                informative: "Informativo",
                selection: "Selección",
                task_with_subtasks: "Subtarea",
                task: "Tarea",
                document: "Documento",
              }}
              items={getUniqueValues("type")}
              sortConfig={sortConfig}
              title="Tipo"
              onFilter={handleFilterChange}
              onSort={handleSort}
            />
          </TableColumn>
          <TableColumn>
            <FilterDropdown
              activeFilters={activeFilters}
              column="phase"
              items={getUniqueValues("phase")}
              sortConfig={sortConfig}
              title="Fase"
              onFilter={handleFilterChange}
              onSort={handleSort}
            />
          </TableColumn>
          <TableColumn>
            <FilterDropdown
              activeFilters={activeFilters}
              column="subphase"
              items={getUniqueValues("subphase")}
              sortConfig={sortConfig}
              title="Subfase"
              onFilter={handleFilterChange}
              onSort={handleSort}
            />
          </TableColumn>
          <TableColumn>
            <FilterDropdown
              activeFilters={activeFilters}
              column="weight"
              items={getUniqueValues("weight")}
              sortConfig={sortConfig}
              title="Peso"
              onFilter={handleFilterChange}
              onSort={handleSort}
            />
          </TableColumn>
        </TableHeader>
        <TableBody emptyContent="No hay campos disponibles" items={items}>
          {(item) => (
            <TableRow key={item?.id}>
              <TableCell>{item?.name}</TableCell>
              <TableCell>
                <div className="max-w-xs">
                  <p className="text-sm text-default-600 line-clamp-2">
                    {item?.description || "Sin descripción"}
                  </p>
                </div>
              </TableCell>
              <TableCell>
                <Chip size="sm" variant="flat">
                  {item?.type?.toLowerCase() === "informative"
                    ? "Informativo"
                    : item?.type?.toLowerCase() === "selection"
                      ? "Selección"
                      : item?.type?.toLowerCase() === "task_with_subtasks"
                        ? "Subtarea"
                        : item?.type?.toLowerCase() === "task"
                          ? "Tarea"
                          : item?.type?.toLowerCase() === "document"
                            ? "Documento"
                            : item?.type?.toLowerCase() || "Desconocido"}
                </Chip>
              </TableCell>
              <TableCell>{item?.subphase?.phase?.name}</TableCell>
              <TableCell>{item?.subphase?.name}</TableCell>
              <TableCell>{item?.weight}</TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  );
}
